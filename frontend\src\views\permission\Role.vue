<template>
  <ArtTableFullScreen>
    <div class="account-page" id="table-full-screen">
      <!-- 搜索栏 -->
      <ArtSearchBar
        v-model:filter="formFilters"
        :items="formItems"
        @reset="handleReset"
        @search="handleSearch"
      ></ArtSearchBar>

      <ElCard shadow="never" class="art-table-card">
        <!-- 表格头部 -->
        <ArtTableHeader :columnList="columnOptions" @refresh="handleRefresh">
          <template #left>
            <ElButton
              v-auth="'system:permission_role:add'"
              @click="showDialog('add')"
              type="primary"
              v-ripple
              >新增
            </ElButton>
          </template>
        </ArtTableHeader>

        <!-- 表格 -->
        <ArtTable
          :loading="loading"
          :data="tableData"
          :currentPage="currentPage"
          :pageSize="pageSize"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :marginTop="10"
        >
          <template #default>
            <!-- 勾选列 -->
            <ElTableColumn type="selection" />

            <!-- 角色名称列 -->
            <ElTableColumn prop="name" label="角色名称" min-width="120" />

            <!-- 数据权限列 -->
            <ElTableColumn prop="data_scope" label="数据权限">
              <template #default="{ row }">
                {{ getDataScopeText(row.data_scope) }}
              </template>
            </ElTableColumn>

            <!-- 状态列 -->
            <ElTableColumn prop="status" label="状态">
              <template #default="{ row }">
                <ElTag :type="getTagType(row.status)">
                  {{ buildTagText(row.status) }}
                </ElTag>
              </template>
            </ElTableColumn>

            <!-- 排序列 -->
            <ElTableColumn prop="sort" label="排序" min-width="100" />

            <!-- 备注列 -->
            <ElTableColumn prop="remark" label="备注" min-width="150" />

            <!-- 创建时间列 -->
            <ElTableColumn prop="created_at" label="创建时间" sortable />

            <!-- 操作列 -->
            <ElTableColumn prop="operation" label="操作" width="200">
              <template #default="{ row }">
                <div>
                  <ArtButtonTable
                    v-if="hasAuth('system:permission_role:edit')"
                    title="编辑"
                    type="edit"
                    @click="showDialog('edit', row)"
                  />
                  <ArtButtonTable
                    v-if="hasAuth('system:permission_role:delete')"
                    title="删除"
                    type="delete"
                    @click="deleteRole(row.id)"
                  />
                </div>
              </template>
            </ElTableColumn>
          </template>
        </ArtTable>

        <ElDialog
          v-model="dialogVisible"
          :title="dialogType === 'add' ? '添加角色' : '编辑角色'"
          width="30%"
        >
          <ElForm
            ref="formRef"
            :model="formData"
            :rules="rules"
            :validate-on-rule-change="false"
            label-width="80px"
          >
            <ElFormItem label="角色名称" prop="name">
              <ElInput v-model="formData.name" />
            </ElFormItem>
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="状态" prop="status">
                  <ElSelect v-model="formData.status" style="width: 100%">
                    <ElOption label="正常" :value="1" />
                    <ElOption label="禁用" :value="0" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="排序" prop="sort">
                  <ElInput v-model="formData.sort" />
                </ElFormItem>
              </ElCol>
            </ElRow>
            <ElFormItem label="菜单权限" prop="menu_ids">
              <div class="menu-tree-container">
                <ElTree
                  ref="treeRef"
                  :data="menuTreeData"
                  :props="defaultProps"
                  node-key="id"
                  show-checkbox
                  :default-expanded-keys="expandedKeys"
                  :default-checked-keys="formData.menu_ids"
                  highlight-current
                  check-on-click-node
                  @check="handleTreeCheck"
                />
              </div>
            </ElFormItem>
            <ElFormItem label="数据权限" prop="data_scope">
              <ElSelect
                v-model="formData.data_scope"
                placeholder="请选择数据权限"
                style="width: 100%"
              >
                <ElOption label="全部" :value="1" />
                <ElOption label="本部门" :value="2" />
                <ElOption label="本部门及以下" :value="3" />
                <ElOption label="仅本人" :value="4" />
                <ElOption label="自定义" :value="5" />
              </ElSelect>
            </ElFormItem>

            <ElFormItem label="备注" prop="remark">
              <ElInput
                v-model="formData.remark"
                type="textarea"
                :rows="4"
                placeholder="请输入备注"
              />
            </ElFormItem>
          </ElForm>
          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="dialogVisible = false">取消</ElButton>
              <ElButton type="primary" @click="handleSubmit">提交</ElButton>
            </div>
          </template>
        </ElDialog>
      </ElCard>
    </div>
  </ArtTableFullScreen>
</template>

<script setup lang="ts">
  import { ElDialog, ElMessage, ElMessageBox, ElTag, ElTree } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import { RoleApi } from '@/api/roleApi'
  import { MenuApi } from '@/api/menuApi'
  import { SearchChangeParams, SearchFormItem } from '@/types/search-form'

  import ArtButtonTable from '@/components/core/forms/ArtButtonTable.vue'
  import { useAuth } from '@/composables/useAuth'
  import { ApiStatus } from '@/utils/http/status'

  // 权限验证
  const { hasAuth } = useAuth()

  onMounted(() => {
    getTableData()
    getMenuOptions()
  })

  const dialogType = ref('add')
  const dialogVisible = ref(false)
  const loading = ref(false)
  const menuTreeData = ref<any[]>([])
  const treeRef = ref<InstanceType<typeof ElTree> | null>(null)

  // 新增：记录用户真实选择的节点
  const userSelectedNodes = ref<Set<number>>(new Set())

  /**
   * 计算展开的节点（包括所有相关的父节点）
   * @param treeData 树形数据
   * @param userSelected 用户实际选择的节点ID数组
   * @returns 应该展开的节点ID数组
   */
  const calculateExpandedKeys = (treeData: any[], userSelected: number[]): number[] => {
    const userSet = new Set(userSelected)
    const shouldExpand = new Set<number>()

    const traverse = (nodes: any[]) => {
      nodes.forEach(node => {
        if (userSet.has(node.id)) {
          // 用户选择的节点，展开其所有父节点
          shouldExpand.add(node.id)
        }

        if (node.children && node.children.length > 0) {
          traverse(node.children)

          // 如果有子节点被选中，父节点也应该被展开
          const hasSelectedChildren = node.children.some((child: any) =>
            userSet.has(child.id) ||
            (child.children && child.children.some((grandChild: any) => userSet.has(grandChild.id)))
          )

          if (hasSelectedChildren) {
            shouldExpand.add(node.id)
          }
        }
      })
    }

    traverse(treeData)
    return Array.from(shouldExpand)
  }

  /**
   * 从当前选中状态分析用户的真实意图
   * @param checkedKeys 当前完全选中的节点
   * @param treeData 树形数据
   * @returns 用户真实选择的节点集合
   */
  const analyzeUserIntent = (checkedKeys: number[], treeData: any[]): Set<number> => {
    const realSelections = new Set<number>()

    // 构建节点映射和层级关系
    const nodeMap = new Map<number, any>()
    const childrenMap = new Map<number, number[]>() // 父节点 -> 子节点列表

    const buildNodeMap = (nodes: any[], parentId: number = 0) => {
      nodes.forEach(node => {
        nodeMap.set(node.id, { ...node, parentId })

        if (node.children && node.children.length > 0) {
          childrenMap.set(node.id, node.children.map((child: any) => child.id))
          buildNodeMap(node.children, node.id)
        } else {
          childrenMap.set(node.id, [])
        }
      })
    }
    buildNodeMap(treeData)

    // 先处理所有叶子节点（没有子节点的节点）
    checkedKeys.forEach(nodeId => {
      const children = childrenMap.get(nodeId) || []
      if (children.length === 0) {
        // 叶子节点，直接认为是用户选择的
        realSelections.add(nodeId)
      }
    })

    // 然后处理父节点，从最深层开始
    const processedNodes = new Set<number>()

    // 按层级从深到浅处理
    const processNodesByLevel = () => {
      let hasChanges = true
      while (hasChanges) {
        hasChanges = false

        checkedKeys.forEach(nodeId => {
          if (processedNodes.has(nodeId)) return

          const children = childrenMap.get(nodeId) || []
          if (children.length === 0) {
            processedNodes.add(nodeId)
            return
          }

          // 检查所有子节点是否都已处理
          const allChildrenProcessed = children.every(childId =>
            processedNodes.has(childId) || !checkedKeys.includes(childId)
          )

          if (allChildrenProcessed) {
            // 检查是否所有子节点都被选中
            const selectedChildren = children.filter(childId => checkedKeys.includes(childId))

            if (selectedChildren.length === children.length && children.length > 0) {
              // 所有子节点都选中，认为用户选择了父节点
              realSelections.add(nodeId)
              // 移除子节点（因为选择父节点就包含了子节点）
              children.forEach(childId => realSelections.delete(childId))
            }

            processedNodes.add(nodeId)
            hasChanges = true
          }
        })
      }
    }

    processNodesByLevel()

    return realSelections
  }

  /*
   * 搜索
   * */
  const initialSearchState = {
    name: '',
    data_scope: '',
    status: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 搜索重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    currentPage.value = 1
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    currentPage.value = 1
    getTableData()
  }

  // 搜索表单项变更处理
  const handleFormChange = (params: SearchChangeParams): void => {
    console.log('表单项变更:', params)
  }

  // 搜索表单配置项
  const formItems: SearchFormItem[] = [
    {
      label: '角色名称',
      prop: 'name',
      type: 'input',
      config: {
        clearable: true
      },
      onChange: handleFormChange
    },
    {
      label: '数据权限',
      prop: 'data_scope',
      type: 'select',
      config: {
        clearable: true
      },
      options: () => [
        { label: '全部', value: 1 },
        { label: '本部门', value: 2 },
        { label: '本部门及以下', value: 3 },
        { label: '仅本人', value: 4 },
        { label: '自定义', value: 5 }
      ],
      onChange: handleFormChange
    },
    {
      label: '状态',
      prop: 'status',
      type: 'select',
      config: {
        clearable: true
      },
      options: () => [
        { label: '正常', value: 1 },
        { label: '禁用', value: 0 }
      ],
      onChange: handleFormChange
    }
  ]

  const getTagType = (status: number) => {
    switch (status) {
      case 1:
        return 'success'
      case 0:
        return 'danger'
      default:
        return 'info'
    }
  }

  // 构建标签文本
  const buildTagText = (status: number) => {
    return status === 1 ? '正常' : '禁用'
  }

  /*
   * 表格
   * */
  // 辅助函数
  const getDataScopeText = (dataScope: number) => {
    const scopeMap: Record<number, string> = {
      1: '全部',
      2: '本部门',
      3: '本部门及以下',
      4: '仅本人',
      5: '自定义'
    }
    return scopeMap[dataScope] || ''
  }

  const columnOptions = [
    { label: '勾选', type: 'selection' },
    { label: '角色名称', prop: 'name' },
    { label: '数据权限', prop: 'data_scope' },
    { label: '排序', prop: 'sort' },
    { label: '状态', prop: 'status' },
    { label: '备注', prop: 'remark' },
    { label: '创建时间', prop: 'created_at' },
    { label: '操作', prop: 'operation' }
  ]

  // 表格数据
  const tableData = ref<any[]>([])

  const currentPage = ref(1),
    pageSize = ref(10),
    total = ref(0)

  const getTableData = async () => {
    loading.value = true
    try {
      const params = {
        page: currentPage.value,
        limit: pageSize.value,
        ...formFilters
      }
      const res = await RoleApi.list(params)
      if (res.code === ApiStatus.success) {
        // 确保res.data是对象而非数组
        const data = res.data as any
        total.value = data.total || 0
        currentPage.value = data.currentPage || 1
        pageSize.value = data.pageSize || 10
        tableData.value = data.list || []
      }
    } catch (error) {
      console.error('获取角色列表失败', error)
    } finally {
      loading.value = false
    }
  }

  // 处理页码变化
  const handleCurrentChange = (page: number) => {
    currentPage.value = page
    getTableData()
  }

  // 处理每页条数变化
  const handleSizeChange = (size: number) => {
    pageSize.value = size
    currentPage.value = 1
    getTableData()
  }

  const handleRefresh = () => {
    getTableData()
  }

  // 表单实例
  const formRef = ref<FormInstance>()

  // 表单数据
  const formData = reactive({
    name: '',
    data_scope: undefined as number | undefined,
    data_scope_dept_ids: [],
    menu_ids: [] as number[],
    sort: 1,
    status: 1,
    remark: ''
  })

  // 表单验证规则
  const rules = reactive<FormRules>({
    name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
    data_scope: [{ required: true, message: '请选择数据权限', trigger: ['change', 'blur'] }]
  })

  const currentId = ref(0)
  const expandedKeys = ref<number[]>([])

  // 获取菜单选项
  const getMenuOptions = async () => {
    try {
      const res = await MenuApi.options()
      if (res.code === ApiStatus.success && res.data) {
        menuTreeData.value = res.data
        // 如果是编辑模式，需要设置展开节点
        if (dialogType.value === 'edit' && formData.menu_ids.length > 0) {
          expandedKeys.value = [...formData.menu_ids]
        }
      }
    } catch (error) {
      console.error('获取菜单选项失败', error)
    }
  }

  // 显示对话框
  const showDialog = async (type: string, row?: any) => {
    // 先显示对话框
    dialogVisible.value = true
    dialogType.value = type

    // 如果存在表单实例，则先重置，避免校验信息残留
    if (formRef.value) {
      formRef.value.resetFields()
    }

    // 重置表单数据
    currentId.value = 0
    formData.name = ''
    formData.remark = ''
    formData.status = 1
    formData.sort = 1
    formData.data_scope = undefined
    formData.menu_ids = []
    expandedKeys.value = []

    // 重置用户选择记录
    userSelectedNodes.value = new Set()

    // 确保有菜单数据
    if (menuTreeData.value.length === 0) {
      await getMenuOptions()
    }

    // 如果是新增，确保清空树的选中状态
    if (type === 'add') {
      await nextTick(() => {
        if (treeRef.value) {
          treeRef.value.setCheckedKeys([])
        }
      })
    }

    if (type === 'edit' && row) {
      try {
        const res = await RoleApi.detail(row.id)
        if (res.code === ApiStatus.success && res.data) {
          const roleData = res.data
          // 使用nextTick避免数据更新触发校验
          await nextTick(() => {
            formData.name = roleData.name
            formData.remark = roleData.remark || ''
            formData.status = roleData.status
            formData.sort = roleData.sort
            formData.data_scope = roleData.data_scope || undefined
            currentId.value = roleData.id

            // 智能初始化：只设置用户真实选择，让ElTree自动处理半选状态
            if (roleData.menu_ids && Array.isArray(roleData.menu_ids)) {
              const userRealSelections = roleData.menu_ids

              // 记录用户真实选择
              userSelectedNodes.value = new Set(userRealSelections)
              formData.menu_ids = userRealSelections

              // 计算展开的节点（包括相关父节点）
              const expandKeys = calculateExpandedKeys(menuTreeData.value, userRealSelections)
              expandedKeys.value = expandKeys

              nextTick(() => {
                if (treeRef.value) {
                  // 关键修改：只设置用户真实选择的节点
                  // ElTree会自动根据子节点选中情况显示父节点的半选状态
                  treeRef.value.setCheckedKeys(userRealSelections)

                  console.log('角色权限初始化:', {
                    用户真实选择: userRealSelections,
                    展开节点: expandKeys,
                    树数据长度: menuTreeData.value.length
                  })
                }
              })
            } else if (roleData.menus && Array.isArray(roleData.menus)) {
              // 兼容旧数据格式
              const userRealSelections = roleData.menus
              userSelectedNodes.value = new Set(userRealSelections)
              formData.menu_ids = userRealSelections

              const expandKeys = calculateExpandedKeys(menuTreeData.value, userRealSelections)
              expandedKeys.value = expandKeys

              nextTick(() => {
                if (treeRef.value) {
                  treeRef.value.setCheckedKeys(userRealSelections)
                }
              })
            } else {
              // 如果没有找到菜单数据，则清空
              userSelectedNodes.value = new Set()
              formData.menu_ids = []
              expandedKeys.value = []
              nextTick(() => {
                if (treeRef.value) {
                  treeRef.value.setCheckedKeys([])
                }
              })
            }
          })
        }
      } catch (error) {
        console.error('获取角色详情失败', error)
      }
    }
  }

  // 删除角色
  const deleteRole = (id: number) => {
    ElMessageBox.confirm('确定要删除该角色吗？', '删除角色', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          const res = await RoleApi.delete([id])
          if (res.code === ApiStatus.success) {
            ElMessage.success('删除成功')
            await getTableData()
          }
        } catch (error) {
          console.error('删除角色失败', error)
        }
      })
      .catch(() => {
        // 取消删除
      })
  }

  // 菜单树配置
  const defaultProps = {
    children: 'children',
    label: 'name',
    value: 'id'
  }

  const showPermissionDialog = async (roleId: number) => {
    await showDialog('edit', { id: roleId })
  }

  // 处理节点选中状态变化
  const handleTreeCheck = (
    node: any,
    { checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys }: any
  ) => {
    console.log('选中节点变化:', {
      操作节点: node,
      完全选中: checkedKeys,
      半选节点: halfCheckedKeys,
      完全选中数量: checkedKeys.length,
      半选数量: halfCheckedKeys.length
    })

    // 分析用户的真实意图
    const newUserSelections = analyzeUserIntent(checkedKeys, halfCheckedKeys, menuTreeData.value)
    userSelectedNodes.value = newUserSelections

    // 更新表单数据（用于显示和调试）
    formData.menu_ids = Array.from(newUserSelections)

    console.log('用户真实选择更新:', {
      之前的选择: Array.from(userSelectedNodes.value),
      当前真实选择: formData.menu_ids
    })
  }

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    // 智能提交：只提交用户真实选择的节点
    const userRealSelections = Array.from(userSelectedNodes.value)
    formData.menu_ids = userRealSelections

    console.log('智能提交分析:', {
      当前树状态: {
        完全选中: treeRef.value?.getCheckedKeys() || [],
        半选节点: treeRef.value?.getHalfCheckedKeys() || []
      },
      用户真实选择: userRealSelections,
      提交数据: formData.menu_ids
    })

    await formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          const submitData = { ...formData }

          if (!Array.isArray(submitData.menu_ids)) {
            submitData.menu_ids = []
          }

          let res
          if (dialogType.value === 'add') {
            res = await RoleApi.add(submitData)
          } else {
            res = await RoleApi.edit(currentId.value, submitData)
          }

          if (res.code === ApiStatus.success) {
            ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功')
            dialogVisible.value = false
            await getTableData()
          }
        } catch (error) {
          console.error(dialogType.value === 'add' ? '添加角色失败' : '更新角色失败', error)
        }
      }
    })
  }
</script>

<style lang="scss" scoped>
  .page-content {
    .svg-icon {
      width: 1.8em;
      height: 1.8em;
      overflow: hidden;
      vertical-align: -8px;
      fill: currentcolor;
    }

    .el-col2 {
      display: flex;
      gap: 10px;
    }
  }

  .menu-tree-container {
    width: 100%;
    height: 220px;
    overflow-y: auto;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 5px;

    .el-tree {
      width: 100%;
      background: transparent;
    }

    .el-tree-node__content {
      height: 32px;
    }

    // 确保鼠标悬停样式
    .el-tree-node:hover > .el-tree-node__content {
      background-color: #f5f7fa;
    }

    // 选中节点的样式
    .el-tree-node.is-current > .el-tree-node__content {
      background-color: #f0f7ff;
      color: #409eff;
    }
  }

  :deep(.menu-tree-dropdown) {
    .el-select-dropdown__wrap {
      max-height: 220px !important;
    }

    .el-tree {
      min-width: 100%;
      display: inline-block !important;
    }

    .el-tree-node {
      white-space: nowrap !important;
    }

    .el-tree-node__content {
      height: 32px;
    }

    // 确保半选状态样式正确显示
    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      background-color: #409eff;
      border-color: #409eff;
    }

    .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
      content: '';
      position: absolute;
      display: block;
      background-color: #fff;
      height: 2px;
      transform: scale(0.5);
      left: 0;
      right: 0;
      top: 5px;
    }
  }

  :deep(.menu-tree-select) {
    width: 100%;
  }
</style>
