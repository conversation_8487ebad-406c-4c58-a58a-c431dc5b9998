<?php
declare(strict_types=1);

namespace app\system\service;

use app\common\core\base\BaseService;
use app\common\core\crud\traits\CrudServiceTrait;
use app\common\exception\BusinessException;
use app\common\exception\ValidateFailedException;
use app\system\model\MenuModel;
use app\system\validate\MenuValidate;

/**
 * 菜单服务类
 */
class MenuService extends BaseService
{
	
	use CrudServiceTrait;
	
	public function __construct()
	{
		$this->model = new MenuModel();
		parent::__construct();
	}
	
	public function getIndexList($param = [])
	{
		$where = [];
		$name  = $param['name'] ?? '';
		if ($name) {
			$where[] = [
				'name',
				'like',
				'%' . $name . '%'
			];
		}
		$list = $this->crudService->getList($where, [
			'sort' => 'desc',
		]);
		return $this->buildMenuTree($list);
	}
	
	/**
	 * 创建菜单
	 *
	 * @param array $data 菜单数据
	 * @return int
	 */
	public function create(array $data): int
	{
		try {
			validate(MenuValidate::class)->check($data);
		}
		catch (ValidateFailedException $e) {
			throw new ValidateFailedException($e->getMessage());
		}
		
		return $this->crudService->add($data);
	}
	
	/**
	 * 更新菜单
	 *
	 * @param int   $id   菜单ID
	 * @param array $data 菜单数据
	 * @return bool
	 */
	public function update(int $id, array $data): bool
	{
		try {
			validate(MenuValidate::class)->check($data);
		}
		catch (ValidateFailedException $e) {
			throw new ValidateFailedException($e->getMessage());
		}
		
		$info = $this->crudService->getOne([
			[
				'id',
				'=',
				$id
			],
		]);
		
		if ($info->isEmpty()) {
			throw new BusinessException('菜单不存在');
		}
		
		if ($id == $info['parent_id']) {
			throw new BusinessException('当前菜单不能作为自己的子菜单');
		}
		
		/*if (isset($data['parent_id']) && $data['parent_id'] > 0) {
			// 获取所有子菜单ID
			$childIds = $this->getChildMenuIds($menuId);
			if (in_array($data['parent_id'], $childIds) || $data['parent_id'] == $menuId) {
				return false;
			}
		}*/
		
		return $info->saveByUpdate($data);
	}
	
	/**
	 * 删除菜单
	 *
	 * @param array $ids 菜单ID
	 * @return bool
	 */
	public function delete(array $ids): bool
	{
		
		$info = $this->crudService->getList([
			[
				'id',
				'in',
				$ids
			],
		]);
		
		if ($info->isEmpty()) {
			throw new BusinessException('菜单不存在');
		}
		
		// 检查是否有子菜单
		$childCount = $this->crudService->getCount([
			[
				'parent_id',
				'in',
				$ids
			]
		]);
		
		if ($childCount > 0) {
			throw new BusinessException('当前菜单有子菜单');
		}
		
		return $info->delete();
	}
	
	
	/**
	 * 构建菜单树形结构（优化版本）
	 *
	 * @param     $list
	 * @param int $parentId 父级ID
	 * @return array
	 */
	public function buildMenuTree($list, int $parentId = 0): array
	{
		if ($parentId === 0) {
			return $this->buildMenuTreeOptimized($list);
		}
		
		$tree = [];
		
		foreach ($list as $item) {
			if ($item['type'] == 2) {
				continue;
			}
			if ($item['parent_id'] == $parentId) {
				
				$children = $this->buildMenuTree($list, $item['id']);
				if (!empty($children)) {
					$item['children'] = $children;
				}
				
				// 性能瓶颈：每个菜单项都要查询权限列表
				$menuAuthList = $list->where('parent_id', $item['id'])
				                     ->where('type', 2);
				
				$authList = [];
				foreach ($menuAuthList as $auth) {
					$authList[] = [
						'id'        => $auth['id'],
						'title'     => $auth['title'],
						'auth_mark' => $auth['name'],
						'name'      => $auth['name'],
					];
				}
				$meta = [
					'title'     => $item['title'],
					'icon'      => $item['icon'],
					'keepAlive' => $item['keep_alive'],
					'isHide'    => $item['visible'] == 0,
					'isIframe'  => $item['external'] == 1,
					'isHideTab' => $item['hide_tab'] == 1,
					'authList'  => $authList,
				];
				if (!empty($item['link'])) {
					$meta['link'] = $item['link'];
				}
				$tree[] = [
					'id'        => $item['id'],
					'title'     => $item['title'],
					'name'      => $item['name'],
					'path'      => $item['path'],
					'component' => $item['component']
						?: ($parentId == 0
							? '/index/index'
							: ''),
					'meta'      => $meta,
					'visible'   => $item['visible'],
					'children'  => $item['children'] ?? [],
				];
			}
		}
		
		return $tree;
	}
	
	/**
	 * 优化的菜单树构建方法
	 * 解决N+1查询问题，提升性能
	 *
	 * @param $list
	 * @return array
	 */
	private function buildMenuTreeOptimized($list): array
	{
		// 转换为数组便于处理
		$items = $list->toArray();
		
		// 预处理：按类型分组，避免重复查询
		$menus       = [];      // 菜单项 (type != 2)
		$permissions = [];      // 权限项 (type == 2)
		
		foreach ($items as $item) {
			if ($item['type'] == 2) {
				$permissions[$item['parent_id']][] = [
					'id'        => $item['id'],
					'title'     => $item['title'],
					'auth_mark' => $item['name'],
					'name'      => $item['name'],
				];
			}
			else {
				$menus[] = $item;
			}
		}
		// 构建树形结构
		return $this->buildTreeRecursive($menus, $permissions, 0);
	}
	
	/**
	 * 递归构建树形结构（优化版本）
	 *
	 * @param array $menus
	 * @param array $permissions
	 * @param int   $parentId
	 * @return array
	 */
	private function buildTreeRecursive(array $menus, array $permissions, int $parentId): array
	{
		$tree = [];
		
		foreach ($menus as $item) {
			if ($item['parent_id'] == $parentId) {
				// 递归获取子菜单
				$children = $this->buildTreeRecursive($menus, $permissions, $item['id']);
				if (!empty($children)) {
					$item['children'] = $children;
				}
				
				// 直接从预处理的权限数组中获取，避免重复查询
				$authList = $permissions[$item['id']] ?? [];
				
				$meta = [
					'title'     => $item['title'],
					'icon'      => $item['icon'],
					'keepAlive' => $item['keep_alive'],
					'isHide'    => $item['visible'] == 0,
					'isIframe'  => $item['external'] == 1,
					'isHideTab' => $item['hide_tab'] == 1,
					'authList'  => $authList,
				];
				if (!empty($item['link'])) {
					$meta['link'] = $item['link'];
				}
				$tree[] = [
					'id'        => $item['id'],
					'title'     => $item['title'],
					'name'      => $item['name'],
					'path'      => $item['path'],
					'component' => $item['component']
						?: ($parentId == 0
							? '/index/index'
							: ''),
					'meta'      => $meta,
					'visible'   => $item['visible'],
					'children'  => $item['children'] ?? [],
				];
			}
		}
		
		return $tree;
	}
	
	/**
	 * 获取用户的菜单列表
	 *
	 * @param int $adminId 用户ID
	 * @return array
	 */
	public function getUserMenus(int $adminId): array
	{
		$permissionService = new PermissionService();
		
		$menuList = $permissionService->getAdminPermissionByAdminId($adminId, true);
		
		if (empty($menuList)) {
			return [];
		}
		
		// 构建树形结构
		return $this->buildMenuTree($menuList);
	}
	
	/**
	 * 获取菜单下拉选项
	 *
	 * @return array
	 */
	public function getOptions(): array
	{
		// 获取菜单列表
		$where    = [
			[
				'status',
				'=',
				1
			],
		];
		$menuList = $this->model->field('id,parent_id,title as name')
		                        ->where($where)
		                        ->order('sort desc')
		                        ->select();
		
		// 构建下拉选项
		return list_to_tree($menuList->toArray());
		
	}
	
}