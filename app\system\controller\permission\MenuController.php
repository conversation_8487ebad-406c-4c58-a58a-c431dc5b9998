<?php

namespace app\system\controller\permission;

use app\common\core\base\BaseAdminController;
use app\system\service\MenuService;
use think\facade\Cache;
use think\response\Json;

/**
 * 菜单权限控制器
 */
class MenuController extends BaseAdminController
{
	/**
	 * @var MenuService
	 */
	private MenuService $service;
	
	public function initialize(): void
	{
		parent::initialize();
		
		$this->service = MenuService::getInstance();
	}
	
	/**
	 * 获取菜单列表
	 */
	public function index(): Json
	{
		$result = $this->service->getIndexList(input());
		
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 获取菜单详情
	 */
	public function detail($id): Json
	{
		return $this->success('获取成功', $this->service->detail((int)$id));
	}
	
	/**
	 * 创建菜单
	 */
	public function add(): Json
	{
		$result = $this->service->create(input());
		
		if (!$result) {
			return $this->error('创建失败');
		}
		
		// 清除菜单缓存
		$this->clearMenuCache();
		
		return $this->success('创建成功');
	}
	
	/**
	 * 更新菜单
	 */
	public function edit($id): Json
	{
		$result = $this->service->update((int)$id, input());
		
		if (!$result) {
			return $this->error('更新失败');
		}
		
		// 清除菜单缓存
		$this->clearMenuCache();
		
		return $this->success('更新成功');
	}
	
	/**
	 * 删除菜单
	 */
	public function delete(): Json
	{
		$ids = input('id/a');
		if (empty($ids)) {
			return $this->error('请选择要删除的菜单');
		}
		$result = $this->service->delete($ids);
		
		if (!$result) {
			return $this->error('请先删除子菜单');
		}
		
		// 清除菜单缓存
		$this->clearMenuCache();
		
		return $this->success('删除成功');
	}
	
	/**
	 * 获取菜单选项（用于下拉框）
	 */
	public function options(): Json
	{
		return $this->success('获取成功', $this->service->getOptions($this->request->get()));
	}
	
	/**
	 * 获取菜单类型信息（用于权限分配智能处理）
	 */
	public function getMenuTypes(): Json
	{
		$ids = input('ids/a', []);
		if (empty($ids)) {
			return $this->error('菜单ID不能为空');
		}
		
		$menus = $this->service->getMenuTypes($ids);
		return $this->success('获取成功', $menus);
	}
	
	/**
	 * 清除菜单缓存
	 */
	private function clearMenuCache(): void
	{
		// 清除所有用户的菜单缓存
		Cache::tag('menu')->clear();
	}
}
